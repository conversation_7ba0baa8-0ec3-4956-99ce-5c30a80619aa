#!/usr/bin/env python3
"""
HVI-RF-DETR特征可视化完整演示
包含中文兼容性和TensorBoard特征图迭代图集
"""
import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 导入我们的可视化模块
try:
    from rfdetr.training.feature_visualization import FeatureVisualizationLogger
    from rfdetr.training.tensorboard_logger import HVIRFDETRLogger
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在rf-detr目录下运行此脚本")
    sys.exit(1)

class MockHVIRFDETRModel(nn.Module):
    """模拟HVI-RF-DETR模型，用于特征可视化演示"""
    
    def __init__(self, input_size=512):
        super().__init__()
        self.input_size = input_size
        
        # HVI增强模块
        self.hvi_enhancer = nn.Sequential(
            nn.Conv2d(3, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.GELU(),
            nn.Conv2d(32, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.GELU(),
            nn.Conv2d(32, 16, 1),  # HVI特征
            nn.Sigmoid()
        )
        
        # RGB增强分支
        self.rgb_enhancer = nn.Sequential(
            nn.Conv2d(3, 16, 3, padding=1),
            nn.BatchNorm2d(16),
            nn.GELU(),
            nn.Conv2d(16, 3, 3, padding=1),
            nn.Sigmoid()
        )
        
        # 骨干网络 (多尺度特征提取)
        self.backbone_stage1 = nn.Sequential(
            nn.Conv2d(3, 64, 7, stride=2, padding=3),
            nn.BatchNorm2d(64),
            nn.GELU(),
            nn.MaxPool2d(3, stride=2, padding=1)
        )
        
        self.backbone_stage2 = nn.Sequential(
            nn.Conv2d(64, 128, 3, stride=2, padding=1),
            nn.BatchNorm2d(128),
            nn.GELU(),
            nn.Conv2d(128, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.GELU()
        )
        
        self.backbone_stage3 = nn.Sequential(
            nn.Conv2d(128, 256, 3, stride=2, padding=1),
            nn.BatchNorm2d(256),
            nn.GELU(),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.BatchNorm2d(256),
            nn.GELU()
        )
        
        # 特征融合模块
        self.fusion_conv = nn.Conv2d(16, 256, 1)  # HVI特征投影
        self.cross_attention = nn.MultiheadAttention(256, 8, batch_first=True)
        
        # 检测头
        self.detection_head = nn.Sequential(
            nn.Conv2d(256, 256, 3, padding=1),
            nn.BatchNorm2d(256),
            nn.GELU(),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.BatchNorm2d(256),
            nn.GELU(),
            nn.AdaptiveAvgPool2d((8, 8)),
            nn.Flatten(),
            nn.Linear(256 * 8 * 8, 1024),
            nn.GELU(),
            nn.Linear(1024, 300 * 85)  # 300 queries, 85 outputs per query
        )
    
    def forward(self, x):
        batch_size = x.size(0)
        
        # HVI增强过程
        hvi_features = self.hvi_enhancer(x)  # (B, 16, H, W)
        enhanced_rgb = self.rgb_enhancer(x)  # (B, 3, H, W)
        
        # 骨干网络多尺度特征提取
        feat1 = self.backbone_stage1(enhanced_rgb)  # (B, 64, H/4, W/4)
        feat2 = self.backbone_stage2(feat1)         # (B, 128, H/8, W/8)
        feat3 = self.backbone_stage3(feat2)         # (B, 256, H/16, W/16)
        
        # 特征融合
        # 将HVI特征调整到与feat3相同的空间尺寸
        hvi_resized = nn.functional.interpolate(
            hvi_features, size=feat3.shape[2:], mode='bilinear', align_corners=False
        )
        hvi_projected = self.fusion_conv(hvi_resized)  # (B, 256, H/16, W/16)
        
        # 交叉注意力融合
        B, C, H, W = feat3.shape
        feat3_flat = feat3.view(B, C, H*W).transpose(1, 2)  # (B, H*W, C)
        hvi_flat = hvi_projected.view(B, C, H*W).transpose(1, 2)  # (B, H*W, C)
        
        # 计算注意力
        attn_output, attn_weights = self.cross_attention(
            feat3_flat, hvi_flat, hvi_flat
        )
        
        # 重塑回空间维度
        fused_features = attn_output.transpose(1, 2).view(B, C, H, W)
        
        # 检测头
        detection_output = self.detection_head(fused_features)
        detection_output = detection_output.view(batch_size, 300, 85)
        
        return {
            'hvi_features': hvi_features,
            'enhanced_rgb': enhanced_rgb,
            'backbone_features': [feat1, feat2, feat3],
            'fused_features': fused_features,
            'attention_weights': attn_weights,
            'detection_output': detection_output
        }

def create_nighttime_test_data(batch_size=4, resolution=512):
    """创建夜间测试数据"""
    images = []
    
    for _ in range(batch_size):
        # 创建夜间图像 (较暗，有噪声)
        base_brightness = np.random.uniform(0.1, 0.3)
        img = torch.rand(3, resolution, resolution) * base_brightness
        
        # 添加噪声
        noise = torch.randn_like(img) * 0.02
        img = img + noise
        
        # 添加一些亮点 (模拟灯光)
        num_lights = np.random.randint(3, 8)
        for _ in range(num_lights):
            x = np.random.randint(0, resolution)
            y = np.random.randint(0, resolution)
            size = np.random.randint(10, 30)
            
            x_start = max(0, x - size // 2)
            x_end = min(resolution, x + size // 2)
            y_start = max(0, y - size // 2)
            y_end = min(resolution, y + size // 2)
            
            img[:, y_start:y_end, x_start:x_end] += np.random.uniform(0.3, 0.8)
        
        # 限制到有效范围
        img = torch.clamp(img, 0, 1)
        images.append(img)
    
    return torch.stack(images)

def run_feature_visualization_demo():
    """运行完整的特征可视化演示"""
    print("=" * 60)
    print("HVI-RF-DETR 特征可视化演示")
    print("=" * 60)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建模型
    model = MockHVIRFDETRModel(input_size=512).to(device)
    model.eval()
    
    # 创建日志记录器
    config = {
        'model': 'HVI-RF-DETR',
        'resolution': 512,
        'enable_feature_visualization': True,
        'experiment_type': 'feature_visualization_demo'
    }
    
    # 创建综合日志记录器
    logger = HVIRFDETRLogger(
        log_dir="runs/feature_demo",
        experiment_name="hvi_rf_detr_feature_visualization",
        config=config
    )
    
    # 创建独立的特征可视化日志记录器
    feature_logger = FeatureVisualizationLogger(
        log_dir="runs/feature_demo_detailed",
        experiment_name="detailed_features"
    )
    
    print("\n开始特征可视化演示...")
    
    # 模拟训练过程
    num_steps = 50
    for step in range(0, num_steps, 5):
        print(f"\nStep {step}/{num_steps}")
        
        # 创建测试数据
        test_images = create_nighttime_test_data(batch_size=4, resolution=512)
        test_images = test_images.to(device)
        
        with torch.no_grad():
            # 前向传播
            outputs = model(test_images)
            
            # 1. 记录输入图像
            feature_logger.log_input_images(test_images, step, "nighttime_input")
            
            # 2. 记录HVI增强过程
            logger.log_hvi_enhancement_features(
                test_images,
                outputs['hvi_features'],
                outputs['enhanced_rgb'],
                step,
                'train'
            )
            
            feature_logger.log_hvi_enhancement_process(
                test_images,
                outputs['hvi_features'],
                outputs['enhanced_rgb'],
                step
            )
            
            # 3. 记录骨干网络特征金字塔
            logger.log_backbone_features(
                outputs['backbone_features'],
                step,
                'train'
            )
            
            feature_logger.log_multi_scale_features(
                outputs['backbone_features'],
                step
            )
            
            # 4. 记录特征融合
            logger.log_fusion_features(
                outputs['fused_features'],
                outputs['attention_weights'],
                step,
                'train'
            )
            
            feature_logger.log_feature_maps(
                outputs['fused_features'],
                "Feature_Fusion",
                step
            )
            
            feature_logger.log_attention_maps(
                outputs['attention_weights'].unsqueeze(0),  # 添加batch维度
                "Cross_Attention",
                step
            )
            
            # 5. 记录检测特征
            logger.log_detection_features(
                outputs['fused_features'],
                step,
                'train'
            )
            
            # 6. 每10步创建综合分析
            if step % 10 == 0:
                logger.create_feature_evolution_summary(step)
                feature_logger.create_comprehensive_feature_summary(step)
            
            # 7. 记录一些训练指标 (模拟)
            loss_dict = {
                'classification': torch.tensor(2.0 * np.exp(-step/20) + 0.1 + np.random.normal(0, 0.05)),
                'bbox_regression': torch.tensor(1.5 * np.exp(-step/25) + 0.05 + np.random.normal(0, 0.03)),
                'eiou': torch.tensor(1.0 * np.exp(-step/30) + 0.02 + np.random.normal(0, 0.02)),
                'focal': torch.tensor(1.8 * np.exp(-step/22) + 0.08 + np.random.normal(0, 0.04))
            }
            
            logger.log_loss_components(loss_dict, step, 'train')
            
            # 记录HVI增强效果指标
            logger.log_hvi_enhancement_metrics(
                test_images, outputs['enhanced_rgb'], step, 'train'
            )
        
        print(f"  - 输入图像已记录")
        print(f"  - HVI增强过程已记录")
        print(f"  - 多尺度特征已记录")
        print(f"  - 特征融合已记录")
        print(f"  - 注意力权重已记录")
    
    print("\n生成特征演化图集...")
    
    # 生成所有特征演化图集
    logger.generate_feature_galleries()
    
    # 关闭日志记录器
    logger.close()
    feature_logger.close()
    
    print("\n" + "=" * 60)
    print("特征可视化演示完成!")
    print("=" * 60)
    print(f"TensorBoard日志: {logger.experiment_dir}")
    print(f"特征图集: {feature_logger.gallery_dir}")
    print(f"启动TensorBoard: tensorboard --logdir {logger.experiment_dir}")
    print("访问地址: http://localhost:6006")
    print("\n生成的可视化内容:")
    print("  ✓ 输入夜间图像序列")
    print("  ✓ HVI色彩空间增强过程")
    print("  ✓ 多尺度骨干特征演化")
    print("  ✓ 交叉注意力权重可视化")
    print("  ✓ 特征融合结果展示")
    print("  ✓ 训练过程损失曲线")
    print("  ✓ 特征演化图集")
    print("  ✓ 综合特征分析报告")

def main():
    """主函数"""
    try:
        run_feature_visualization_demo()
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
