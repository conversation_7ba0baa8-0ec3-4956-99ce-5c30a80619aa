2025-06-23T12:16:40.009Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-23T12:16:40.010Z [MCPServer] INFO: Parsed options: {}
2025-06-23T12:16:40.010Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-23T12:16:40.011Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-23T12:16:40.011Z [MCPServer] INFO: Current options: {}
2025-06-23T12:16:40.011Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-23T12:16:40.014Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-23T12:16:40.014Z [MCPServer] INFO: Starting relay server...
2025-06-23T12:16:41.241Z [MCPServer] INFO: Relay server started successfully
2025-06-23T12:16:41.241Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-23T12:16:41.263Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-23T12:16:41.264Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
2025-06-23T12:20:48.572Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-23T12:20:48.573Z [MCPServer] INFO: Parsed options: {}
2025-06-23T12:20:48.574Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-23T12:20:48.574Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-23T12:20:48.574Z [MCPServer] INFO: Current options: {}
2025-06-23T12:20:48.574Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-23T12:20:48.577Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-23T12:20:48.578Z [MCPServer] INFO: Starting relay server...
2025-06-23T12:20:49.829Z [MCPServer] INFO: Relay server started successfully
2025-06-23T12:20:49.829Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-23T12:20:49.853Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-23T12:20:49.853Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
2025-06-23T12:21:20.683Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-23T12:21:20.684Z [MCPServer] INFO: Parsed options: {}
2025-06-23T12:21:20.684Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-23T12:21:20.685Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-23T12:21:20.685Z [MCPServer] INFO: Current options: {}
2025-06-23T12:21:20.685Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-23T12:21:20.689Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-23T12:21:20.689Z [MCPServer] INFO: Starting relay server...
2025-06-23T12:21:21.906Z [MCPServer] INFO: Relay server started successfully
2025-06-23T12:21:21.906Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-23T12:21:21.931Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-23T12:21:21.931Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
2025-06-23T15:46:29.341Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-23T15:46:29.343Z [MCPServer] INFO: Parsed options: {}
2025-06-23T15:46:29.343Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-23T15:46:29.343Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-23T15:46:29.343Z [MCPServer] INFO: Current options: {}
2025-06-23T15:46:29.344Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-23T15:46:29.347Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-23T15:46:29.347Z [MCPServer] INFO: Starting relay server...
2025-06-23T15:46:30.596Z [MCPServer] INFO: Relay server started successfully
2025-06-23T15:46:30.596Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-23T15:46:30.617Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-23T15:46:30.618Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
2025-06-24T01:29:55.488Z [MCPServer] INFO: Processing 0 command line arguments: 
2025-06-24T01:29:55.490Z [MCPServer] INFO: Parsed options: {}
2025-06-24T01:29:55.490Z [MCPServer] INFO: Setting environment variables from command line options
2025-06-24T01:29:55.491Z [MCPServer] INFO: Found relay server at path: d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\relay-server\dist\index.js
2025-06-24T01:29:55.491Z [MCPServer] INFO: Current options: {}
2025-06-24T01:29:55.491Z [MCPServer] INFO: Command line arguments: D:\Program Files\nodejs\node.exe d:\vscode\Roo_Code\MCP\vrchat-mcp-osc\packages\mcp-server\dist\server.js
2025-06-24T01:29:55.494Z [MCPServer] INFO: Initializing VRChat OSC MCP server
2025-06-24T01:29:55.494Z [MCPServer] INFO: Starting relay server...
2025-06-24T01:29:56.778Z [MCPServer] INFO: Relay server started successfully
2025-06-24T01:29:56.778Z [MCPServer] INFO: Connecting to WebSocket server...
2025-06-24T01:29:58.815Z [MCPServer] INFO: Connecting to MCP transport...
2025-06-24T01:29:58.816Z [MCPServer] INFO: VRChat OSC MCP server initialized successfully
