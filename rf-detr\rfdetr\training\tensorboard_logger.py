"""
TensorBoard集成训练监控系统
用于记录HVI-RF-DETR训练过程中的各项指标
"""
import torch
from torch.utils.tensorboard import SummaryWriter
import numpy as np
from pathlib import Path
import time
import json
from typing import Dict, Any, Optional, List
import matplotlib.pyplot as plt
import seaborn as sns

class HVIRFDETRLogger:
    """HVI-RF-DETR专用TensorBoard日志记录器"""
    
    def __init__(self, 
                 log_dir: str = "runs/hvi_rf_detr",
                 experiment_name: str = None,
                 config: Dict = None):
        """
        初始化日志记录器
        
        Args:
            log_dir: 日志保存目录
            experiment_name: 实验名称
            config: 实验配置
        """
        self.log_dir = Path(log_dir)
        
        # 创建实验特定的目录
        if experiment_name:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            self.experiment_dir = self.log_dir / f"{experiment_name}_{timestamp}"
        else:
            self.experiment_dir = self.log_dir / time.strftime("%Y%m%d_%H%M%S")
        
        self.experiment_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建不同阶段的writer
        self.writers = {
            'train': SummaryWriter(self.experiment_dir / 'train'),
            'val': SummaryWriter(self.experiment_dir / 'val'),
            'test': SummaryWriter(self.experiment_dir / 'test')
        }
        
        # 步数计数器
        self.step_counters = {'train': 0, 'val': 0, 'test': 0}
        
        # 保存配置
        if config:
            self.save_config(config)
        
        # 指标历史记录
        self.metrics_history = {
            'train': {},
            'val': {},
            'test': {}
        }
        
        print(f"TensorBoard日志保存到: {self.experiment_dir}")
        print(f"启动TensorBoard: tensorboard --logdir {self.experiment_dir}")
    
    def save_config(self, config: Dict):
        """保存实验配置"""
        config_file = self.experiment_dir / "config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    
    def log_scalar(self, tag: str, value: float, step: Optional[int] = None, phase: str = 'train'):
        """记录标量值"""
        if step is None:
            step = self.step_counters[phase]
            self.step_counters[phase] += 1
        
        self.writers[phase].add_scalar(tag, value, step)
        
        # 保存到历史记录
        if tag not in self.metrics_history[phase]:
            self.metrics_history[phase][tag] = []
        self.metrics_history[phase][tag].append((step, value))
    
    def log_scalars(self, tag_dict: Dict[str, float], step: Optional[int] = None, phase: str = 'train'):
        """批量记录标量值"""
        if step is None:
            step = self.step_counters[phase]
        
        for tag, value in tag_dict.items():
            self.log_scalar(tag, value, step, phase)
    
    def log_loss_components(self, loss_dict: Dict[str, torch.Tensor], step: Optional[int] = None, phase: str = 'train'):
        """记录损失函数各组件"""
        loss_values = {}
        
        for loss_name, loss_tensor in loss_dict.items():
            if isinstance(loss_tensor, torch.Tensor):
                loss_values[f'Loss/{loss_name}'] = loss_tensor.item()
            else:
                loss_values[f'Loss/{loss_name}'] = float(loss_tensor)
        
        # 计算总损失
        total_loss = sum(loss_values.values())
        loss_values['Loss/Total'] = total_loss
        
        self.log_scalars(loss_values, step, phase)
        
        return total_loss
    
    def log_detection_metrics(self, metrics: Dict[str, float], step: Optional[int] = None, phase: str = 'val'):
        """记录检测精度指标"""
        detection_metrics = {}
        
        for metric_name, value in metrics.items():
            detection_metrics[f'Detection/{metric_name}'] = value
        
        self.log_scalars(detection_metrics, step, phase)
    
    def log_performance_metrics(self, metrics: Dict[str, float], step: Optional[int] = None, phase: str = 'test'):
        """记录性能指标"""
        performance_metrics = {}
        
        for metric_name, value in metrics.items():
            performance_metrics[f'Performance/{metric_name}'] = value
        
        self.log_scalars(performance_metrics, step, phase)
    
    def log_learning_rate(self, optimizer, step: Optional[int] = None, phase: str = 'train'):
        """记录学习率"""
        if hasattr(optimizer, 'param_groups'):
            for i, param_group in enumerate(optimizer.param_groups):
                lr = param_group['lr']
                self.log_scalar(f'Learning_Rate/group_{i}', lr, step, phase)
    
    def log_gradient_norm(self, model, step: Optional[int] = None, phase: str = 'train'):
        """记录梯度范数"""
        total_norm = 0
        param_count = 0
        
        for name, param in model.named_parameters():
            if param.grad is not None:
                param_norm = param.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
                param_count += 1
                
                # 记录各层梯度范数
                self.log_scalar(f'Gradient_Norm/{name}', param_norm.item(), step, phase)
        
        if param_count > 0:
            total_norm = total_norm ** (1. / 2)
            self.log_scalar('Gradient_Norm/Total', total_norm, step, phase)
        
        return total_norm
    
    def log_model_parameters(self, model, step: Optional[int] = None, phase: str = 'train'):
        """记录模型参数分布"""
        for name, param in model.named_parameters():
            if param.requires_grad:
                self.writers[phase].add_histogram(f'Parameters/{name}', param, step)
                
                # 记录参数统计信息
                param_stats = {
                    f'Param_Stats/{name}/mean': param.mean().item(),
                    f'Param_Stats/{name}/std': param.std().item(),
                    f'Param_Stats/{name}/min': param.min().item(),
                    f'Param_Stats/{name}/max': param.max().item()
                }
                self.log_scalars(param_stats, step, phase)
    
    def log_hvi_enhancement_metrics(self, original_img, enhanced_img, step: Optional[int] = None, phase: str = 'train'):
        """记录HVI增强效果指标"""
        with torch.no_grad():
            # 计算增强效果指标
            brightness_improvement = enhanced_img.mean() - original_img.mean()
            contrast_improvement = enhanced_img.std() - original_img.std()
            
            # 计算色彩保真度 (简化版)
            color_fidelity = torch.cosine_similarity(
                original_img.flatten(), enhanced_img.flatten(), dim=0
            )
            
            enhancement_metrics = {
                'HVI_Enhancement/brightness_improvement': brightness_improvement.item(),
                'HVI_Enhancement/contrast_improvement': contrast_improvement.item(),
                'HVI_Enhancement/color_fidelity': color_fidelity.item()
            }
            
            self.log_scalars(enhancement_metrics, step, phase)
    
    def log_attention_weights(self, attention_weights, layer_name: str, step: Optional[int] = None, phase: str = 'train'):
        """记录注意力权重分布"""
        if isinstance(attention_weights, torch.Tensor):
            # 记录注意力权重直方图
            self.writers[phase].add_histogram(f'Attention/{layer_name}', attention_weights, step)
            
            # 记录注意力统计信息
            attention_stats = {
                f'Attention_Stats/{layer_name}/mean': attention_weights.mean().item(),
                f'Attention_Stats/{layer_name}/std': attention_weights.std().item(),
                f'Attention_Stats/{layer_name}/entropy': self._compute_entropy(attention_weights).item()
            }
            self.log_scalars(attention_stats, step, phase)
    
    def _compute_entropy(self, attention_weights):
        """计算注意力权重的熵"""
        # 归一化注意力权重
        probs = torch.softmax(attention_weights.flatten(), dim=0)
        # 计算熵
        entropy = -torch.sum(probs * torch.log(probs + 1e-8))
        return entropy
    
    def log_feature_maps(self, feature_maps: torch.Tensor, tag: str, step: Optional[int] = None, phase: str = 'train'):
        """记录特征图"""
        if len(feature_maps.shape) == 4:  # (B, C, H, W)
            # 选择第一个batch和前几个通道
            feature_sample = feature_maps[0, :min(16, feature_maps.size(1))]  # 最多16个通道
            
            # 创建网格显示
            grid = torch.utils.tensorboard.utils.make_grid(
                feature_sample.unsqueeze(1), nrow=4, normalize=True
            )
            self.writers[phase].add_image(f'Features/{tag}', grid, step)
    
    def log_detection_results(self, images, predictions, targets, step: Optional[int] = None, phase: str = 'val'):
        """记录检测结果可视化"""
        # 这里可以添加检测结果的可视化代码
        # 由于复杂性，这里提供一个简化版本
        if len(images.shape) == 4:  # (B, C, H, W)
            # 记录原始图像
            img_grid = torch.utils.tensorboard.utils.make_grid(
                images[:min(4, images.size(0))], nrow=2, normalize=True
            )
            self.writers[phase].add_image('Detection/input_images', img_grid, step)
    
    def log_model_graph(self, model, input_tensor):
        """记录模型计算图"""
        try:
            self.writers['train'].add_graph(model, input_tensor)
        except Exception as e:
            print(f"无法记录模型图: {e}")
    
    def create_summary_report(self, epoch: int):
        """创建训练总结报告"""
        report = {
            'epoch': epoch,
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
            'metrics_summary': {}
        }
        
        # 汇总各阶段的最新指标
        for phase in ['train', 'val', 'test']:
            phase_summary = {}
            for metric_name, history in self.metrics_history[phase].items():
                if history:
                    latest_value = history[-1][1]
                    phase_summary[metric_name] = latest_value
            report['metrics_summary'][phase] = phase_summary
        
        # 保存报告
        report_file = self.experiment_dir / f"summary_epoch_{epoch}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return report
    
    def plot_training_curves(self, save_path: Optional[str] = None):
        """绘制训练曲线"""
        if not save_path:
            save_path = self.experiment_dir / "training_curves.png"
        
        # 提取损失和精度曲线
        train_loss = self.metrics_history['train'].get('Loss/Total', [])
        val_loss = self.metrics_history['val'].get('Loss/Total', [])
        val_map = self.metrics_history['val'].get('Detection/mAP', [])
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 训练损失
        if train_loss:
            steps, values = zip(*train_loss)
            ax1.plot(steps, values, label='Train Loss', color='blue')
            ax1.set_title('Training Loss')
            ax1.set_xlabel('Step')
            ax1.set_ylabel('Loss')
            ax1.grid(True, alpha=0.3)
        
        # 验证损失
        if val_loss:
            steps, values = zip(*val_loss)
            ax2.plot(steps, values, label='Validation Loss', color='red')
            ax2.set_title('Validation Loss')
            ax2.set_xlabel('Epoch')
            ax2.set_ylabel('Loss')
            ax2.grid(True, alpha=0.3)
        
        # 验证精度
        if val_map:
            steps, values = zip(*val_map)
            ax3.plot(steps, values, label='mAP', color='green')
            ax3.set_title('Validation mAP')
            ax3.set_xlabel('Epoch')
            ax3.set_ylabel('mAP')
            ax3.grid(True, alpha=0.3)
        
        # 学习率曲线
        lr_history = self.metrics_history['train'].get('Learning_Rate/group_0', [])
        if lr_history:
            steps, values = zip(*lr_history)
            ax4.plot(steps, values, label='Learning Rate', color='orange')
            ax4.set_title('Learning Rate Schedule')
            ax4.set_xlabel('Step')
            ax4.set_ylabel('Learning Rate')
            ax4.set_yscale('log')
            ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def close(self):
        """关闭所有writer"""
        for writer in self.writers.values():
            writer.close()
        
        # 生成最终报告
        self.plot_training_curves()
        print(f"训练日志已保存到: {self.experiment_dir}")

# 使用示例
def example_usage():
    """使用示例"""
    
    # 创建日志记录器
    config = {
        'model': 'HVI-RF-DETR',
        'resolution': 512,
        'batch_size': 4,
        'learning_rate': 1e-4
    }
    
    logger = HVIRFDETRLogger(
        experiment_name="hvi_rf_detr_nighttime",
        config=config
    )
    
    # 模拟训练过程
    for epoch in range(10):
        # 训练阶段
        for step in range(100):
            # 模拟损失
            loss_dict = {
                'classification': np.random.uniform(0.5, 1.0),
                'bbox_regression': np.random.uniform(0.3, 0.8),
                'giou': np.random.uniform(0.2, 0.6),
                'eiou': np.random.uniform(0.1, 0.5)
            }
            
            total_loss = logger.log_loss_components(loss_dict, phase='train')
            
            # 记录其他指标
            logger.log_scalar('Learning_Rate/group_0', 1e-4 * (0.9 ** epoch), phase='train')
        
        # 验证阶段
        val_metrics = {
            'mAP': np.random.uniform(0.3, 0.8),
            'mAP_50': np.random.uniform(0.4, 0.9),
            'mAP_small': np.random.uniform(0.2, 0.6)
        }
        logger.log_detection_metrics(val_metrics, epoch, 'val')
        
        # 性能测试
        perf_metrics = {
            'fps': np.random.uniform(30, 100),
            'latency_ms': np.random.uniform(10, 30),
            'memory_mb': np.random.uniform(150, 200)
        }
        logger.log_performance_metrics(perf_metrics, epoch, 'test')
        
        # 创建周期性报告
        if epoch % 5 == 0:
            logger.create_summary_report(epoch)
    
    # 关闭日志记录器
    logger.close()

if __name__ == "__main__":
    example_usage()
