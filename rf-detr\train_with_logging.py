#!/usr/bin/env python3
"""
HVI-RF-DETR训练脚本 - 集成TensorBoard监控
"""
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import argparse
import logging
from pathlib import Path
import sys
import time
import json

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from rfdetr.training.tensorboard_logger import HVIRFDETRLogger
from rfdetr.config.accuracy_improvements import get_accuracy_improvement_config

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('training.log')
        ]
    )

class MockDataset:
    """模拟数据集用于演示"""
    def __init__(self, size=1000, resolution=512):
        self.size = size
        self.resolution = resolution
    
    def __len__(self):
        return self.size
    
    def __getitem__(self, idx):
        # 模拟夜间图像 (较暗)
        image = torch.rand(3, self.resolution, self.resolution) * 0.3
        
        # 模拟目标
        target = {
            'boxes': torch.rand(5, 4),  # 5个随机框
            'labels': torch.randint(0, 80, (5,)),  # COCO类别
            'image_id': torch.tensor(idx)
        }
        
        return image, target

class MockModel(nn.Module):
    """模拟HVI-RF-DETR模型"""
    def __init__(self, num_classes=80, resolution=512):
        super().__init__()
        self.num_classes = num_classes
        self.resolution = resolution
        
        # HVI增强模块
        self.hvi_enhancer = nn.Sequential(
            nn.Conv2d(3, 16, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(16, 3, 3, padding=1),
            nn.Sigmoid()
        )
        
        # 骨干网络
        self.backbone = nn.Sequential(
            nn.Conv2d(3, 64, 7, stride=2, padding=3),
            nn.ReLU(),
            nn.MaxPool2d(3, stride=2, padding=1),
            nn.Conv2d(64, 128, 3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool2d((8, 8))
        )
        
        # 检测头
        self.detection_head = nn.Sequential(
            nn.Flatten(),
            nn.Linear(128 * 8 * 8, 1024),
            nn.ReLU(),
            nn.Linear(1024, 300 * (num_classes + 5))  # 300 queries
        )
        
    def forward(self, x):
        # HVI增强
        enhanced = self.hvi_enhancer(x)
        
        # 骨干特征提取
        features = self.backbone(enhanced)
        
        # 检测输出
        output = self.detection_head(features)
        output = output.view(x.size(0), 300, self.num_classes + 5)
        
        return {
            'pred_logits': output[:, :, :self.num_classes],
            'pred_boxes': output[:, :, self.num_classes:self.num_classes+4],
            'enhanced_image': enhanced,
            'backbone_features': features
        }

class MockCriterion(nn.Module):
    """模拟损失函数"""
    def __init__(self):
        super().__init__()
        self.cls_loss = nn.CrossEntropyLoss()
        self.bbox_loss = nn.L1Loss()
        
    def forward(self, outputs, targets):
        # 模拟各种损失
        batch_size = outputs['pred_logits'].size(0)
        
        # 分类损失
        cls_loss = torch.rand(1).item() * 2.0
        
        # 边界框损失
        bbox_loss = torch.rand(1).item() * 1.0
        
        # GIoU损失
        giou_loss = torch.rand(1).item() * 0.5
        
        # EIoU损失 (我们的改进)
        eiou_loss = torch.rand(1).item() * 0.3
        
        # Focal损失 (处理类别不平衡)
        focal_loss = torch.rand(1).item() * 1.5
        
        loss_dict = {
            'classification': torch.tensor(cls_loss, requires_grad=True),
            'bbox_regression': torch.tensor(bbox_loss, requires_grad=True),
            'giou': torch.tensor(giou_loss, requires_grad=True),
            'eiou': torch.tensor(eiou_loss, requires_grad=True),
            'focal': torch.tensor(focal_loss, requires_grad=True)
        }
        
        return loss_dict

def train_epoch(model, dataloader, criterion, optimizer, logger, epoch, device):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    num_batches = len(dataloader)
    
    for batch_idx, (images, targets) in enumerate(dataloader):
        images = images.to(device)
        
        # 前向传播
        outputs = model(images)
        
        # 计算损失
        loss_dict = criterion(outputs, targets)
        total_loss_value = logger.log_loss_components(loss_dict, phase='train')
        
        # 反向传播
        optimizer.zero_grad()
        total_loss_tensor = sum(loss_dict.values())
        total_loss_tensor.backward()
        
        # 记录梯度范数
        grad_norm = logger.log_gradient_norm(model, phase='train')
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        # 记录学习率
        logger.log_learning_rate(optimizer, phase='train')
        
        # 记录HVI增强效果
        if batch_idx % 50 == 0:
            logger.log_hvi_enhancement_metrics(
                images, outputs['enhanced_image'], phase='train'
            )
            
            # 记录特征图
            logger.log_feature_maps(
                outputs['backbone_features'], 'backbone_features', phase='train'
            )
        
        # 记录模型参数 (每100步)
        if batch_idx % 100 == 0:
            logger.log_model_parameters(model, phase='train')
        
        total_loss += total_loss_value
        
        # 打印进度
        if batch_idx % 50 == 0:
            print(f'Epoch {epoch}, Batch {batch_idx}/{num_batches}, '
                  f'Loss: {total_loss_value:.4f}, Grad Norm: {grad_norm:.4f}')
    
    return total_loss / num_batches

def validate_epoch(model, dataloader, criterion, logger, epoch, device):
    """验证一个epoch"""
    model.eval()
    total_loss = 0
    
    # 模拟检测指标
    detection_metrics = {
        'mAP': np.random.uniform(0.3, 0.8),
        'mAP_50': np.random.uniform(0.4, 0.9),
        'mAP_75': np.random.uniform(0.2, 0.7),
        'mAP_small': np.random.uniform(0.1, 0.5),
        'mAP_medium': np.random.uniform(0.3, 0.7),
        'mAP_large': np.random.uniform(0.5, 0.9)
    }
    
    with torch.no_grad():
        for batch_idx, (images, targets) in enumerate(dataloader):
            images = images.to(device)
            
            # 前向传播
            outputs = model(images)
            
            # 计算损失
            loss_dict = criterion(outputs, targets)
            total_loss_value = logger.log_loss_components(loss_dict, phase='val')
            
            total_loss += total_loss_value
            
            # 记录检测结果可视化 (第一个batch)
            if batch_idx == 0:
                logger.log_detection_results(images, outputs, targets, epoch, 'val')
    
    # 记录检测指标
    logger.log_detection_metrics(detection_metrics, epoch, 'val')
    
    return total_loss / len(dataloader), detection_metrics

def test_performance(model, dataloader, logger, epoch, device):
    """测试性能指标"""
    model.eval()
    
    latencies = []
    memory_usage = []
    
    with torch.no_grad():
        for batch_idx, (images, targets) in enumerate(dataloader):
            if batch_idx >= 50:  # 只测试50个batch
                break
                
            images = images.to(device)
            
            # 测量推理时间
            torch.cuda.synchronize() if torch.cuda.is_available() else None
            start_time = time.perf_counter()
            
            outputs = model(images)
            
            torch.cuda.synchronize() if torch.cuda.is_available() else None
            end_time = time.perf_counter()
            
            latency = (end_time - start_time) * 1000  # ms
            latencies.append(latency)
            
            # 记录内存使用
            if torch.cuda.is_available():
                memory_mb = torch.cuda.memory_allocated() / 1024 / 1024
                memory_usage.append(memory_mb)
    
    # 计算性能指标
    avg_latency = np.mean(latencies)
    avg_fps = 1000.0 / avg_latency
    avg_memory = np.mean(memory_usage) if memory_usage else 0
    
    performance_metrics = {
        'fps': avg_fps,
        'latency_ms': avg_latency,
        'memory_mb': avg_memory,
        'realtime_capable': avg_fps >= 30
    }
    
    logger.log_performance_metrics(performance_metrics, epoch, 'test')
    
    return performance_metrics

def main():
    parser = argparse.ArgumentParser(description='HVI-RF-DETR训练脚本')
    parser.add_argument('--config', type=str, default='balanced', 
                       choices=['fast', 'balanced', 'accurate'],
                       help='配置预设')
    parser.add_argument('--epochs', type=int, default=20, help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=4, help='批大小')
    parser.add_argument('--resolution', type=int, default=512, help='图像分辨率')
    parser.add_argument('--device', type=str, default='auto', help='设备')
    parser.add_argument('--experiment-name', type=str, default='hvi_rf_detr_demo',
                       help='实验名称')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging()
    logger_std = logging.getLogger(__name__)
    
    # 设置设备
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    
    logger_std.info(f"使用设备: {device}")
    
    # 获取配置
    config = get_accuracy_improvement_config(args.config)
    
    # 更新配置
    config['training'] = {
        'epochs': args.epochs,
        'batch_size': args.batch_size,
        'resolution': args.resolution,
        'device': str(device)
    }
    
    # 创建TensorBoard日志记录器
    tb_logger = HVIRFDETRLogger(
        experiment_name=args.experiment_name,
        config=config
    )
    
    # 创建数据集和数据加载器
    train_dataset = MockDataset(size=1000, resolution=args.resolution)
    val_dataset = MockDataset(size=200, resolution=args.resolution)
    test_dataset = MockDataset(size=100, resolution=args.resolution)
    
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False)  # 性能测试用单batch
    
    # 创建模型
    model = MockModel(num_classes=80, resolution=args.resolution).to(device)
    
    # 记录模型结构
    sample_input = torch.randn(1, 3, args.resolution, args.resolution).to(device)
    tb_logger.log_model_graph(model, sample_input)
    
    # 创建损失函数和优化器
    criterion = MockCriterion()
    optimizer = optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)
    
    logger_std.info("开始训练...")
    
    best_map = 0.0
    
    for epoch in range(args.epochs):
        logger_std.info(f"Epoch {epoch+1}/{args.epochs}")
        
        # 训练
        train_loss = train_epoch(model, train_loader, criterion, optimizer, tb_logger, epoch, device)
        
        # 验证
        val_loss, val_metrics = validate_epoch(model, val_loader, criterion, tb_logger, epoch, device)
        
        # 性能测试
        perf_metrics = test_performance(model, test_loader, tb_logger, epoch, device)
        
        # 更新学习率
        scheduler.step()
        
        # 记录epoch总结
        epoch_summary = {
            'epoch': epoch,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'val_mAP': val_metrics['mAP'],
            'fps': perf_metrics['fps'],
            'latency_ms': perf_metrics['latency_ms']
        }
        
        logger_std.info(f"Epoch {epoch+1} 总结:")
        logger_std.info(f"  训练损失: {train_loss:.4f}")
        logger_std.info(f"  验证损失: {val_loss:.4f}")
        logger_std.info(f"  验证mAP: {val_metrics['mAP']:.4f}")
        logger_std.info(f"  FPS: {perf_metrics['fps']:.1f}")
        logger_std.info(f"  延迟: {perf_metrics['latency_ms']:.1f}ms")
        logger_std.info(f"  实时性: {'✓' if perf_metrics['realtime_capable'] else '✗'}")
        
        # 保存最佳模型
        if val_metrics['mAP'] > best_map:
            best_map = val_metrics['mAP']
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_map': best_map,
                'config': config
            }, f'best_model_{args.experiment_name}.pth')
            logger_std.info(f"保存最佳模型 (mAP: {best_map:.4f})")
        
        # 创建周期性报告
        if epoch % 5 == 0:
            tb_logger.create_summary_report(epoch)
    
    logger_std.info("训练完成!")
    logger_std.info(f"最佳mAP: {best_map:.4f}")
    
    # 关闭TensorBoard日志记录器
    tb_logger.close()
    
    logger_std.info(f"TensorBoard日志保存在: {tb_logger.experiment_dir}")
    logger_std.info(f"启动TensorBoard: tensorboard --logdir {tb_logger.experiment_dir}")

if __name__ == "__main__":
    main()
