{"name": "ee01c593-ca413bc9", "frames": [{"timestamp": 10000, "objects": [{"category": "car", "id": 0, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 138.819006, "y1": 242.235257, "x2": 251.386976, "y2": 278.452778}}, {"category": "traffic sign", "id": 1, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 658.441054, "y1": 173.256786, "x2": 677.039239, "y2": 191.85497}}, {"category": "traffic sign", "id": 2, "attributes": {"occluded": false, "truncated": true, "trafficLightColor": "none"}, "box2d": {"x1": 95.492428, "y1": 0.560799, "x2": 148.350432, "y2": 56.253395}}, {"category": "traffic sign", "id": 3, "attributes": {"occluded": true, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 85.961004, "y1": 57.232248, "x2": 166.226863, "y2": 132.603847}}, {"category": "car", "id": 4, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 561.208421, "y1": 242.755272, "x2": 584.700866, "y2": 258.416904}}, {"category": "car", "id": 5, "attributes": {"occluded": false, "truncated": false, "trafficLightColor": "none"}, "box2d": {"x1": 586.658571, "y1": 232.966754, "x2": 633.643463, "y2": 277.993942}}, {"category": "area/drivable", "id": 6, "attributes": {}, "poly2d": [[545.268518, 262.028349, "L"], [0, 369.335197, "L"], [0, 539.029747, "L"], [430.475145, 521.561191, "C"], [685.016969, 504.092633, "C"], [1186.614095, 570.223598, "C"], [682.521462, 279.496907, "L"], [580.205629, 281.992414, "L"], [577.710122, 262.028349, "L"], [545.268518, 262.028349, "L"]]}, {"category": "lane/road curb", "id": 7, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[645.335948, 258.733763, "L"], [1207.757218, 573.409961, "L"]]}, {"category": "lane/road curb", "id": 8, "attributes": {"direction": "parallel", "style": "solid"}, "poly2d": [[536.447995, 261.730679, "L"], [0, 371.617606, "L"]]}]}], "attributes": {"weather": "undefined", "scene": "highway", "timeofday": "daytime"}}