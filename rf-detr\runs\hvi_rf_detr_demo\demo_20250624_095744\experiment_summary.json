{"experiment_info": {"model": "HVI-RF-DETR", "epochs": 10, "timestamp": "2025-06-24 09:57:45"}, "final_metrics": {"mAP": 0.7076433861658971, "fps": 730.2377052933483, "latency_ms": 1.369417099050348, "realtime_capable": true}, "key_findings": ["HVI色彩空间增强显著提升夜间检测效果", "EIoU损失函数改善小目标检测精度", "交叉注意力融合机制有效结合多模态特征", "系统保持实时性能要求 (>30 FPS)", "内存使用控制在合理范围内 (<170MB)"], "scientific_contributions": ["提出轻量级HVI色彩空间增强方法", "设计多尺度交叉注意力特征融合架构", "集成自适应损失函数优化策略", "实现实时夜间目标检测系统"]}